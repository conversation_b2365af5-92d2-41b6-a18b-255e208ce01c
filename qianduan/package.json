{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"ahooks": "^3.9.0", "framer-motion": "^12.23.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "styled-components": "^6.1.19"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "ignorePatterns": ["src/qudong/wangluo<PERSON>u/**/*", "public/qudong/wangluo<PERSON>u/**/*"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2"}}