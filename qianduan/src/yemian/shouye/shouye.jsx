import React from 'react';
import { Helmet } from 'react-helmet-async';
import Dingbudaohanglan from '../../zujian/dingbudaohanglan/dingbudaohanglan.jsx';
import { tiqu_dingbu_daohang_xinxi, cong_huancun_duqu_wanzheng_shuju } from '../../gongju/wangzhanjichuxinxi_huancun.js';

const Shouye = () => {
  const [daohang_shuju, shezhi_daohang_shuju] = React.useState({
    wangzhan_mingcheng: 'RO百科',
    wangzhan_tubiao_lianjie: 'https://pan.new-cdn.com/f/GONUG/yunluo.jpg',
    dingbu_daohang: []
  });

  // 加载导航数据
  React.useEffect(() => {
    const wanzheng_shuju = cong_huancun_duqu_wanzheng_shuju();
    if (wanzheng_shuju) {
      const daohang_xinxi = tiqu_dingbu_daohang_xinxi(wanzheng_shuju);
      if (daohang_xinxi) {
        shezhi_daohang_shuju({
          wangzhan_mingcheng: daohang_xinxi.wangzhan_mingcheng || 'RO百科',
          wangzhan_tubiao_lianjie: daohang_xinxi.wangzhan_tubiao_lianjie || 'https://pan.new-cdn.com/f/GONUG/yunluo.jpg',
          dingbu_daohang: daohang_xinxi.dingbu_daohang || []
        });
      }
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>RO百科 - 仙境传说资料站</title>
        <meta name="description" content="仙境传说资料站，提供怪物、物品、地图、技能等游戏数据查询" />
      </Helmet>
      
      <Dingbudaohanglan
        wangzhanmingcheng={daohang_shuju.wangzhan_mingcheng}
        wangzhanlogo={daohang_shuju.wangzhan_tubiao_lianjie}
        caidanxiangmu={daohang_shuju.dingbu_daohang}
        xianshi={true}
      />
      
      <div style={{ padding: '20px', marginTop: '80px' }}>
        <h1>欢迎来到RO百科</h1>
        <p>这里是仙境传说的资料站，您可以查询各种游戏数据。</p>
        
        <div style={{ marginTop: '30px' }}>
          <h2>功能导航</h2>
          <ul>
            <li><a href="/guaiwushuju">怪物数据</a> - 查询怪物信息</li>
            <li><a href="/wupinshuju">物品数据</a> - 查询物品信息</li>
            <li><a href="/ditushuju">地图数据</a> - 查询地图信息</li>
            <li><a href="/jinengshuju">技能数据</a> - 查询技能信息</li>
          </ul>
        </div>
      </div>
    </>
  );
};

export default Shouye;
