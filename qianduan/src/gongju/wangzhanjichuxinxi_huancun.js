import cuowurizhi from './chushihua_cuowurizhi.js';

/**
 * 网站基础信息缓存管理模块
 */
const huancun_peizhi = {
  cunchu_jianming: 'wangzhanjichuxinxi',
  youxia<PERSON><PERSON>_haomiao: 2 * 60 * 60 * 1000,
  shijianchu_ziduan: 'huancun_shijianchu',
  shuju_ziduan: 'huancun_shuju'
};



function tiqu_wangzhan_jichuxinxi(xiangying_shuju) {
  try {
    if (!xiangying_shuju || !xiangying_shuju.shuju) {
      console.error(cuowurizhi.xiangyingshuju_jiegou_buzhengque, xiangying_shuju);
      return null;
    }
    const shuju = xiangying_shuju.shuju;
    return {
      wangzhan_tubiao_lianjie: shuju.wangzhan_tubiao_lianjie || '',
      wangzhan_mingcheng: shuju.wangzhan_mingcheng || ''
    };
  } catch (cuowu) {
    console.error(cuowurizhi.tiqu_wangzhan_jichuxinxi_shibai, cuowu);
    return null;
  }
}

// 新增：提取顶部导航信息
function tiqu_dingbu_daohang_xinxi(wanzheng_xiangying_shuju) {
  try {
    if (!wanzheng_xiangying_shuju || !wanzheng_xiangying_shuju.shuju) {
      console.error(cuowurizhi.xiangyingshuju_jiegou_buzhengque, wanzheng_xiangying_shuju);
      return null;
    }

    const shuju = wanzheng_xiangying_shuju.shuju;

    return {
      wangzhan_mingcheng: shuju.wangzhan_mingcheng || 'RO百科',
      wangzhan_tubiao_lianjie: shuju.wangzhan_tubiao_lianjie || '',
      dingbu_daohang: shuju.dingbu_daohang || [],
      seo_xinxi: shuju.seo_xinxi || {},
      shijian: wanzheng_xiangying_shuju.shijian || Date.now()
    };
  } catch (cuowu) {
    console.error('提取顶部导航信息失败:', cuowu);
    return null;
  }
}

function xuliehua_shuju(shuju) {
  try {
    const json_zifuchuan = JSON.stringify(shuju);
    const bianmaqi = new TextEncoder();
    return bianmaqi.encode(json_zifuchuan).buffer;
  } catch (cuowu) {
    console.error(cuowurizhi.shuju_xuliehua_shibai, cuowu);
    return null;
  }
}

function fanxuliehua_shuju(erjinzhi_shuju) {
  try {
    const jiemaqi = new TextDecoder();
    const uint8_shuju = new Uint8Array(erjinzhi_shuju);
    const json_zifuchuan = jiemaqi.decode(uint8_shuju);
    return JSON.parse(json_zifuchuan);
  } catch (cuowu) {
    console.error(cuowurizhi.shuju_fanxuliehua_shibai, cuowu);
    return null;
  }
}

function arraybuffer_dao_base64(buffer) {
  try {
    const uint8_shuzu = new Uint8Array(buffer);
    let erjinzhi_zifuchuan = '';
    for (let i = 0; i < uint8_shuzu.length; i++) {
      erjinzhi_zifuchuan += String.fromCharCode(uint8_shuzu[i]);
    }
    return btoa(erjinzhi_zifuchuan);
  } catch (cuowu) {
    console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai, cuowu);
    return null;
  }
}

function base64_dao_arraybuffer(base64_zifuchuan) {
  try {
    const erjinzhi_zifuchuan = atob(base64_zifuchuan);
    const uint8_shuzu = new Uint8Array(erjinzhi_zifuchuan.length);
    for (let i = 0; i < erjinzhi_zifuchuan.length; i++) {
      uint8_shuzu[i] = erjinzhi_zifuchuan.charCodeAt(i);
    }
    return uint8_shuzu.buffer;
  } catch (cuowu) {
    console.error(cuowurizhi.base64_zhuan_arraybuffer_shibai, cuowu);
    return null;
  }
}

// 存储完整的网络响应数据（包含时间戳）
function cunchu_wanzheng_xiangying_shuju(wanzheng_xiangying_shuju) {
  try {
    const xuliehua_jieguo = xuliehua_shuju(wanzheng_xiangying_shuju);
    if (!xuliehua_jieguo) {
      console.error(cuowurizhi.shuju_xuliehua_shibai);
      return false;
    }
    const base64_shuju = arraybuffer_dao_base64(xuliehua_jieguo);
    if (!base64_shuju) {
      console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai);
      return false;
    }
    const huancun_duixiang = {
      [huancun_peizhi.shuju_ziduan]: base64_shuju,
      [huancun_peizhi.shijianchu_ziduan]: Date.now()
    };

    localStorage.setItem(
      huancun_peizhi.cunchu_jianming,
      JSON.stringify(huancun_duixiang)
    );
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.cunchu_dao_huancun_shibai, cuowu);
    return false;
  }
}

// 保持向后兼容的存储函数
function cunchu_dao_huancun(wangzhan_xinxi) {
  try {
    const xuliehua_jieguo = xuliehua_shuju(wangzhan_xinxi);
    if (!xuliehua_jieguo) {
      console.error(cuowurizhi.shuju_xuliehua_shibai);
      return false;
    }
    const base64_shuju = arraybuffer_dao_base64(xuliehua_jieguo);
    if (!base64_shuju) {
      console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai);
      return false;
    }
    const huancun_duixiang = {
      [huancun_peizhi.shuju_ziduan]: base64_shuju,
      [huancun_peizhi.shijianchu_ziduan]: Date.now()
    };

    localStorage.setItem(
      huancun_peizhi.cunchu_jianming,
      JSON.stringify(huancun_duixiang)
    );
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.cunchu_dao_huancun_shibai, cuowu);
    return false;
  }
}

// 读取完整的缓存数据
function cong_huancun_duqu_wanzheng_shuju() {
  try {
    const huancun_zifuchuan = localStorage.getItem(huancun_peizhi.cunchu_jianming);
    if (!huancun_zifuchuan) {
      return null;
    }

    const huancun_duixiang = JSON.parse(huancun_zifuchuan);
    if (!huancun_duixiang ||
      !huancun_duixiang[huancun_peizhi.shuju_ziduan] ||
      !huancun_duixiang[huancun_peizhi.shijianchu_ziduan]) {
      console.error(cuowurizhi.huancun_shuju_geshi_buzhengque);
      return null;
    }

    const dangqian_shijian = Date.now();
    const huancun_shijian = huancun_duixiang[huancun_peizhi.shijianchu_ziduan];
    const shijian_cha = dangqian_shijian - huancun_shijian;

    if (shijian_cha > huancun_peizhi.youxiaoqi_haomiao) {
      localStorage.removeItem(huancun_peizhi.cunchu_jianming);
      return null;
    }

    const base64_shuju = huancun_duixiang[huancun_peizhi.shuju_ziduan];

    const arraybuffer_shuju = base64_dao_arraybuffer(base64_shuju);
    if (!arraybuffer_shuju) {
      console.error(cuowurizhi.base64_shuju_zhuanhuan_shibai);
      return null;
    }

    const wanzheng_shuju = fanxuliehua_shuju(arraybuffer_shuju);
    if (!wanzheng_shuju) {
      console.error(cuowurizhi.shuju_fanxuliehua_shibai_cong_huancun);
      return null;
    }

    return wanzheng_shuju;
  } catch (cuowu) {
    console.error(cuowurizhi.cong_huancun_duqu_shibai, cuowu);
    return null;
  }
}

// 保持向后兼容的读取函数
function cong_huancun_duqu() {
  const wanzheng_shuju = cong_huancun_duqu_wanzheng_shuju();
  if (!wanzheng_shuju) {
    return null;
  }

  // 如果是完整的响应数据，提取基础信息
  if (wanzheng_shuju.shuju) {
    return tiqu_wangzhan_jichuxinxi(wanzheng_shuju);
  }

  // 如果是旧格式的数据，直接返回
  return wanzheng_shuju;
}

function qingchu_huancun() {
  try {
    localStorage.removeItem(huancun_peizhi.cunchu_jianming);
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.qingchu_huancun_shibai, cuowu);
    return false;
  }
}

function jiancha_huancun_youxiao() {
  return cong_huancun_duqu() !== null;
}

export {
  tiqu_wangzhan_jichuxinxi,
  tiqu_dingbu_daohang_xinxi,
  cunchu_dao_huancun,
  cunchu_wanzheng_xiangying_shuju,
  cong_huancun_duqu,
  cong_huancun_duqu_wanzheng_shuju,
  qingchu_huancun,
  jiancha_huancun_youxiao
};
