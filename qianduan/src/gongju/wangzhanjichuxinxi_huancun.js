import cuowurizhi from './chushihua_cuowurizhi.js';

/**
 * 网站基础信息缓存管理模块
 */
const huancun_peizhi = {
  cunchu_jianming: 'wangzhanjichuxinxi',
  youxiaoqi_haomiao: 2 * 60 * 60 * 1000,
  shijianchu_ziduan: 'huancun_shijianchu',
  shuju_ziduan: 'huancun_shuju'
};

// 打印加密存储基础信息
function dayin_jiamicunchu_jichuxinxi() {
  console.log('=== 加密存储基础信息 ===');
  console.log('缓存配置:', {
    存储键名: huancun_peizhi.cunchu_jianming,
    有效期毫秒: huancun_peizhi.youxiaoqi_haomiao,
    有效期小时: huancun_peizhi.youxiaoqi_haomiao / (60 * 60 * 1000),
    时间戳字段: huancun_peizhi.shijianchu_ziduan,
    数据字段: huancun_peizhi.shuju_ziduan
  });
  console.log('存储方式: localStorage');
  console.log('数据格式: JSON -> TextEncoder -> ArrayBuffer -> Base64');
  console.log('加密方式: Base64编码 (非真正加密，仅编码)');
  console.log('========================');
}

function tiqu_wangzhan_jichuxinxi(xiangying_shuju) {
  try {
    if (!xiangying_shuju || !xiangying_shuju.shuju) {
      console.error(cuowurizhi.xiangyingshuju_jiegou_buzhengque, xiangying_shuju);
      return null;
    }
    const shuju = xiangying_shuju.shuju;
    return {
      wangzhan_tubiao_lianjie: shuju.wangzhan_tubiao_lianjie || '',
      wangzhan_mingcheng: shuju.wangzhan_mingcheng || ''
    };
  } catch (cuowu) {
    console.error(cuowurizhi.tiqu_wangzhan_jichuxinxi_shibai, cuowu);
    return null;
  }
}

// 新增：提取顶部导航信息
function tiqu_dingbu_daohang_xinxi(wanzheng_xiangying_shuju) {
  try {
    if (!wanzheng_xiangying_shuju || !wanzheng_xiangying_shuju.shuju) {
      console.error(cuowurizhi.xiangyingshuju_jiegou_buzhengque, wanzheng_xiangying_shuju);
      return null;
    }

    const shuju = wanzheng_xiangying_shuju.shuju;
    console.log('提取导航信息 - 完整数据:', wanzheng_xiangying_shuju);

    return {
      wangzhan_mingcheng: shuju.wangzhan_mingcheng || 'RO百科',
      wangzhan_tubiao_lianjie: shuju.wangzhan_tubiao_lianjie || '',
      dingbu_daohang: shuju.dingbu_daohang || [],
      seo_xinxi: shuju.seo_xinxi || {},
      shijian: wanzheng_xiangying_shuju.shijian || Date.now()
    };
  } catch (cuowu) {
    console.error('提取顶部导航信息失败:', cuowu);
    return null;
  }
}

function xuliehua_shuju(shuju) {
  try {
    const json_zifuchuan = JSON.stringify(shuju);
    const bianmaqi = new TextEncoder();
    return bianmaqi.encode(json_zifuchuan).buffer;
  } catch (cuowu) {
    console.error(cuowurizhi.shuju_xuliehua_shibai, cuowu);
    return null;
  }
}

function fanxuliehua_shuju(erjinzhi_shuju) {
  try {
    const jiemaqi = new TextDecoder();
    const uint8_shuju = new Uint8Array(erjinzhi_shuju);
    const json_zifuchuan = jiemaqi.decode(uint8_shuju);
    return JSON.parse(json_zifuchuan);
  } catch (cuowu) {
    console.error(cuowurizhi.shuju_fanxuliehua_shibai, cuowu);
    return null;
  }
}

function arraybuffer_dao_base64(buffer) {
  try {
    const uint8_shuzu = new Uint8Array(buffer);
    let erjinzhi_zifuchuan = '';
    for (let i = 0; i < uint8_shuzu.length; i++) {
      erjinzhi_zifuchuan += String.fromCharCode(uint8_shuzu[i]);
    }
    return btoa(erjinzhi_zifuchuan);
  } catch (cuowu) {
    console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai, cuowu);
    return null;
  }
}

function base64_dao_arraybuffer(base64_zifuchuan) {
  try {
    const erjinzhi_zifuchuan = atob(base64_zifuchuan);
    const uint8_shuzu = new Uint8Array(erjinzhi_zifuchuan.length);
    for (let i = 0; i < erjinzhi_zifuchuan.length; i++) {
      uint8_shuzu[i] = erjinzhi_zifuchuan.charCodeAt(i);
    }
    return uint8_shuzu.buffer;
  } catch (cuowu) {
    console.error(cuowurizhi.base64_zhuan_arraybuffer_shibai, cuowu);
    return null;
  }
}

// 存储完整的网络响应数据（包含时间戳）
function cunchu_wanzheng_xiangying_shuju(wanzheng_xiangying_shuju) {
  dayin_jiamicunchu_jichuxinxi();
  console.log('存储操作 - 完整响应数据:', wanzheng_xiangying_shuju);

  try {
    const xuliehua_jieguo = xuliehua_shuju(wanzheng_xiangying_shuju);
    if (!xuliehua_jieguo) {
      console.error(cuowurizhi.shuju_xuliehua_shibai);
      return false;
    }
    const base64_shuju = arraybuffer_dao_base64(xuliehua_jieguo);
    if (!base64_shuju) {
      console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai);
      return false;
    }
    const huancun_duixiang = {
      [huancun_peizhi.shuju_ziduan]: base64_shuju,
      [huancun_peizhi.shijianchu_ziduan]: Date.now()
    };

    console.log('存储操作 - 最终存储对象:', huancun_duixiang);
    console.log('存储操作 - Base64数据长度:', base64_shuju.length);
    console.log('存储操作 - 原始数据包含时间戳:', wanzheng_xiangying_shuju.shijian);

    localStorage.setItem(
      huancun_peizhi.cunchu_jianming,
      JSON.stringify(huancun_duixiang)
    );
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.cunchu_dao_huancun_shibai, cuowu);
    return false;
  }
}

// 保持向后兼容的存储函数
function cunchu_dao_huancun(wangzhan_xinxi) {
  dayin_jiamicunchu_jichuxinxi();
  console.log('存储操作 - 输入数据:', wangzhan_xinxi);

  try {
    const xuliehua_jieguo = xuliehua_shuju(wangzhan_xinxi);
    if (!xuliehua_jieguo) {
      console.error(cuowurizhi.shuju_xuliehua_shibai);
      return false;
    }
    const base64_shuju = arraybuffer_dao_base64(xuliehua_jieguo);
    if (!base64_shuju) {
      console.error(cuowurizhi.arraybuffer_zhuan_base64_shibai);
      return false;
    }
    const huancun_duixiang = {
      [huancun_peizhi.shuju_ziduan]: base64_shuju,
      [huancun_peizhi.shijianchu_ziduan]: Date.now()
    };

    console.log('存储操作 - 最终存储对象:', huancun_duixiang);
    console.log('存储操作 - Base64数据长度:', base64_shuju.length);

    localStorage.setItem(
      huancun_peizhi.cunchu_jianming,
      JSON.stringify(huancun_duixiang)
    );
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.cunchu_dao_huancun_shibai, cuowu);
    return false;
  }
}

// 读取完整的缓存数据
function cong_huancun_duqu_wanzheng_shuju() {
  dayin_jiamicunchu_jichuxinxi();
  console.log('读取操作 - 开始从缓存读取完整数据');

  try {
    const huancun_zifuchuan = localStorage.getItem(huancun_peizhi.cunchu_jianming);
    if (!huancun_zifuchuan) {
      console.log('读取操作 - 缓存为空');
      return null;
    }

    console.log('读取操作 - 原始缓存字符串长度:', huancun_zifuchuan.length);

    const huancun_duixiang = JSON.parse(huancun_zifuchuan);
    if (!huancun_duixiang ||
      !huancun_duixiang[huancun_peizhi.shuju_ziduan] ||
      !huancun_duixiang[huancun_peizhi.shijianchu_ziduan]) {
      console.error(cuowurizhi.huancun_shuju_geshi_buzhengque);
      return null;
    }

    console.log('读取操作 - 缓存对象:', huancun_duixiang);

    const dangqian_shijian = Date.now();
    const huancun_shijian = huancun_duixiang[huancun_peizhi.shijianchu_ziduan];
    const shijian_cha = dangqian_shijian - huancun_shijian;

    console.log('读取操作 - 时间检查:', {
      当前时间: dangqian_shijian,
      缓存时间: huancun_shijian,
      时间差毫秒: shijian_cha,
      有效期毫秒: huancun_peizhi.youxiaoqi_haomiao,
      是否过期: shijian_cha > huancun_peizhi.youxiaoqi_haomiao
    });

    if (shijian_cha > huancun_peizhi.youxiaoqi_haomiao) {
      console.log('读取操作 - 缓存已过期，清除缓存');
      localStorage.removeItem(huancun_peizhi.cunchu_jianming);
      return null;
    }

    const base64_shuju = huancun_duixiang[huancun_peizhi.shuju_ziduan];
    console.log('读取操作 - Base64数据长度:', base64_shuju.length);

    const arraybuffer_shuju = base64_dao_arraybuffer(base64_shuju);
    if (!arraybuffer_shuju) {
      console.error(cuowurizhi.base64_shuju_zhuanhuan_shibai);
      return null;
    }

    const wanzheng_shuju = fanxuliehua_shuju(arraybuffer_shuju);
    if (!wanzheng_shuju) {
      console.error(cuowurizhi.shuju_fanxuliehua_shibai_cong_huancun);
      return null;
    }

    console.log('读取操作 - 最终解析完整数据:', wanzheng_shuju);
    return wanzheng_shuju;
  } catch (cuowu) {
    console.error(cuowurizhi.cong_huancun_duqu_shibai, cuowu);
    return null;
  }
}

// 保持向后兼容的读取函数
function cong_huancun_duqu() {
  const wanzheng_shuju = cong_huancun_duqu_wanzheng_shuju();
  if (!wanzheng_shuju) {
    return null;
  }

  // 如果是完整的响应数据，提取基础信息
  if (wanzheng_shuju.shuju) {
    return tiqu_wangzhan_jichuxinxi(wanzheng_shuju);
  }

  // 如果是旧格式的数据，直接返回
  return wanzheng_shuju;
}

function qingchu_huancun() {
  dayin_jiamicunchu_jichuxinxi();
  console.log('清除操作 - 开始清除缓存');

  try {
    localStorage.removeItem(huancun_peizhi.cunchu_jianming);
    console.log('清除操作 - 缓存清除成功');
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.qingchu_huancun_shibai, cuowu);
    return false;
  }
}

function jiancha_huancun_youxiao() {
  console.log('检查操作 - 开始检查缓存有效性');
  const jieguo = cong_huancun_duqu() !== null;
  console.log('检查操作 - 缓存有效性结果:', jieguo);
  return jieguo;
}

export {
  tiqu_wangzhan_jichuxinxi,
  tiqu_dingbu_daohang_xinxi,
  cunchu_dao_huancun,
  cunchu_wanzheng_xiangying_shuju,
  cong_huancun_duqu,
  cong_huancun_duqu_wanzheng_shuju,
  qingchu_huancun,
  jiancha_huancun_youxiao
};
