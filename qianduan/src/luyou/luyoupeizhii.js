import React from 'react';

// 懒加载页面组件
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = React.lazy(() => import('../yemian/guaiwushuju/guaiwushuju.jsx'));
const Wupinshuju = React.lazy(() => import('../yemian/wupinshuju/wupinshuju.jsx'));
const Ditushuju = React.lazy(() => import('../yemian/ditushuju/ditushuju.jsx'));
const Jin<PERSON>shu<PERSON> = React.lazy(() => import('../yemian/jinengshuju/jinengshuju.jsx'));

// 路由配置
export const luyou_peizhi = [
  {
    lujing: '/guaiwushuju',
    zujian: Guaiwushuju,
    biaoti: '怪物数据 - RO百科',
    miaoshu: '仙境传说怪物数据查询'
  },
  {
    lujing: '/wupinshuju',
    zujian: Wupinshuju,
    biaoti: '物品数据 - RO百科',
    miaoshu: '仙境传说物品数据查询'
  },
  {
    lujing: '/ditushuju',
    zujian: Di<PERSON>hu<PERSON>,
    biaoti: '地图数据 - RO百科',
    miaoshu: '仙境传说地图数据查询'
  },
  {
    lujing: '/jinengshuju',
    zujian: Jinengshuju,
    biaoti: '技能数据 - RO百科',
    miaoshu: '仙境传说技能数据查询'
  }
];
